<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.usefulteam.easy_social_share_example">
    <application
        android:label="easy_social_share_example"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme"
            />
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT" />
            <data android:mimeType="text/plain" />
        </intent>
    </queries>
    <queries>
        <!-- Explicit apps you know in advance about: -->

        <package android:name="com.instagram.android" />
        <package android:name="com.zhiliaoapp.musically" />
        <package android:name="com.facebook.katana" />
        <package android:name="com.facebook.orca" />
        <package android:name="org.telegram.messenger" />
        <package android:name="com.whatsapp" />
        <package android:name="com.twitter.android" />

        <provider android:authorities="com.facebook.katana.provider.PlatformProvider" /> <!-- allows
        app to access Facebook app features -->
        <provider android:authorities="com.facebook.orca.provider.PlatformProvider" /> <!-- allows
        sharing to Messenger app -->
    </queries>

    <!-- Required only if your app needs to access images or photos
       that other apps created. -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <!-- Required only if your app needs to access videos
       that other apps created. -->
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />

    <!-- Required only if your app needs to access audio files
       that other apps created. -->
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="35" />
    <uses-permission android:name="android.permission.INTERNET" />
</manifest>